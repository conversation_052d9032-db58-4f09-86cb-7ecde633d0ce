# OCS-Flyright Configuration System

## Overview

The OCS-Flyright backup/restore system has been updated to use a centralized INI-based configuration system. This allows for easy management of all system variables including serial numbers, paths, database locations, and other settings without modifying the source code.

## Configuration File

The main configuration file is located at:
```
/home/<USER>/Desktop/clonezilla-master/ocs-flyright.ini
```

## Configuration Sections

### [database]
- `database_path`: Main drive database file location
- `table_name`: Database table name for drives
- `log_database_base_path`: Base directory for simulator-specific log databases

### [paths]
- `drbl_script_path`: DRBL script directory
- `ocs_sr_path`: Path to Clonezilla's ocs-sr command
- `functions_file`: Path to ocs-flyright-functions file
- `partimag_path`: Directory where backup images are stored
- `remote_backup_mount`: Mount point for remote backup disks
- `temp_dir`: Temporary files directory

### [config_files]
- Various system configuration file paths

### [device_serials]
- Serial numbers for all archive disks and devices
- `c208`, `efis`, `ka_g1000`, `c90`, `pl21`, `dash`, `internal`, `os`
- `remote_backup`: Serial number for remote backup disk

### [excluded_wwids]
- List of WWIDs to exclude from device selection
- Format: `wwid_1`, `wwid_2`, etc.

### [simulator_mappings]
- Maps archive disk selections to simulator names
- Used for automatic simulator detection

### [ui]
- `dialog_tool`: User interface tool (whiptail)
- `language`: System language setting

### [backup_settings]
- `backup_params`: Default ocs-sr parameters for backup operations
- `restore_params`: Default ocs-sr parameters for restore operations
- `dd_block_size`: Block size for DD operations

### [sd_card]
- UHS-II slot configuration parameters
- `enable_uhs2_optimization`: Enable/disable UHS-II optimizations

### [dash_system]
- Special configuration for dash backup systems
- `log_filename`: Log database filename for dash systems

### [logging]
- Debug and logging configuration

### [system_devices]
- System device path patterns

## How It Works

### INI File Reading Function

Both scripts now include a `read_ini_value()` function that parses the INI file:

```bash
read_ini_value "section_name" "key_name"
```

### Configuration Loading

Variables are loaded at script startup with fallback defaults:

```bash
database_path=$(read_ini_value "database" "database_path")
database_path="${database_path:-/default/path/here}"
```

### Benefits

1. **Centralized Configuration**: All settings in one file
2. **Easy Maintenance**: No need to edit source code for configuration changes
3. **Fallback Defaults**: Scripts work even if INI file is missing or incomplete
4. **Documentation**: INI file serves as documentation of all configurable options
5. **Version Control**: Configuration changes can be tracked separately from code

## Testing

A test script `test-config.sh` is provided to verify the configuration system:

```bash
./test-config.sh
```

This will display all loaded configuration values and verify the INI parsing is working correctly.

## Modifying Configuration

To change any setting:

1. Edit the `ocs-flyright.ini` file
2. Modify the desired value
3. Save the file
4. The changes will take effect on the next script run

### Example: Changing Database Path

```ini
[database]
database_path = /new/path/to/database.db
```

### Example: Adding New Device Serial

```ini
[device_serials]
new_device = ABC123XYZ
```

### Example: Modifying Backup Parameters

```ini
[backup_settings]
backup_params = -q2 -c -j2 -z1p -i 8192 -sfsck -scs -senc -p true
```

## Backward Compatibility

The scripts maintain backward compatibility by:
- Using fallback defaults if INI file is missing
- Preserving original functionality when configuration values are empty
- Maintaining the same command-line interface

## Files Modified

1. `usr/sbin/ocs-flyright` - Main script
2. `usr/sbin/ocs-flyright-functions` - Functions library
3. `ocs-flyright.ini` - New configuration file
4. `test-config.sh` - Configuration test script

## Variables Converted to Configuration

### Serial Numbers
- All device serial numbers (c208, efis, ka_g1000, etc.)
- Remote backup disk serial number

### Paths and Directories
- Database paths
- System paths (DRBL, ocs-sr, functions)
- Mount points and temporary directories

### System Settings
- WWID exclusion list
- Simulator mappings
- UI preferences
- Backup/restore parameters

### Hardware Configuration
- SD card UHS-II settings
- Dash system special configurations

This configuration system makes the OCS-Flyright system much more maintainable and adaptable to different environments and requirements.
