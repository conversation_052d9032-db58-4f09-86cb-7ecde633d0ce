#!/bin/bash
#
# test-ocs-resize-image-size.sh - Test script for ocs-resize-image-size
#
# This script creates a mock Clonezilla image structure for testing purposes
#

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test directory
TEST_DIR="/tmp/ocs-resize-test"
TEST_IMAGE="test-image"
TEST_IMAGE_DIR="$TEST_DIR/$TEST_IMAGE"

echo -e "${BLUE}=== Testing ocs-resize-image-size ===${NC}"
echo

# Clean up any previous test
if [ -d "$TEST_DIR" ]; then
    echo "Cleaning up previous test..."
    rm -rf "$TEST_DIR"
fi

# Create test directory structure
echo "Creating test image structure..."
mkdir -p "$TEST_IMAGE_DIR"

# Create a mock pt.parted file (simulating a 1TB drive)
cat > "$TEST_IMAGE_DIR/sda-pt.parted" << 'EOF'
Model: ATA Samsung SSD 860 (scsi)
Disk /dev/sda: 1953525168s
Sector size (logical/physical): 512B/512B
Partition Table: gpt
Disk Flags: 

Number  Start      End          Size         File system  Name                          Flags
 1      2048s      1050623s     1048576s     fat32        EFI System Partition          boot, esp
 2      1050624s   1953523711s  1952473088s  ext4         Linux filesystem
EOF

# Create a mock pt.parted.compact file
cat > "$TEST_IMAGE_DIR/sda-pt.parted.compact" << 'EOF'
Model: ATA Samsung SSD 860 (scsi)
Disk /dev/sda: 1953525168s
Sector size (logical/physical): 512B/512B
Partition Table: gpt
Disk Flags: 

Number  Start      End          Size         File system  Name                          Flags
 1      2048s      1050623s     1048576s     fat32        EFI System Partition          boot, esp
 2      1050624s   1953523711s  1952473088s  ext4         Linux filesystem
EOF

# Create Info-img-size.txt
cat > "$TEST_IMAGE_DIR/Info-img-size.txt" << 'EOF'
Image size (Bytes):
2.5G	/tmp/ocs-resize-test/test-image
EOF

# Create parts file
cat > "$TEST_IMAGE_DIR/parts" << 'EOF'
sda1
sda2
EOF

# Create disk file
echo "sda" > "$TEST_IMAGE_DIR/disk"

echo -e "${GREEN}Test image created successfully!${NC}"
echo "Test image location: $TEST_IMAGE_DIR"
echo

# Test 1: Show help
echo -e "${YELLOW}Test 1: Show help${NC}"
./ocs-resize-image-size --help
echo

# Test 2: Show image info
echo -e "${YELLOW}Test 2: Show current image information${NC}"
./ocs-resize-image-size --ocsroot "$TEST_DIR" --info "$TEST_IMAGE"
echo

# Test 3: Dry run
echo -e "${YELLOW}Test 3: Dry run - resize to 500GB${NC}"
./ocs-resize-image-size --ocsroot "$TEST_DIR" --dry-run "$TEST_IMAGE" 500
echo

# Test 4: Actual modification with backup
echo -e "${YELLOW}Test 4: Actual modification with backup and verbose output${NC}"
./ocs-resize-image-size --ocsroot "$TEST_DIR" -v -b -f "$TEST_IMAGE" 500
echo

# Test 5: Show modified image info
echo -e "${YELLOW}Test 5: Show modified image information${NC}"
./ocs-resize-image-size --ocsroot "$TEST_DIR" --info "$TEST_IMAGE"
echo

# Test 6: Verify backup files were created
echo -e "${YELLOW}Test 6: Check backup files${NC}"
echo "Backup files created:"
find "$TEST_IMAGE_DIR" -name "*.backup.*" -ls
echo

# Test 7: Show the actual changes made
echo -e "${YELLOW}Test 7: Show actual file changes${NC}"
echo "Original pt.parted file (from backup):"
backup_file=$(find "$TEST_IMAGE_DIR" -name "sda-pt.parted.backup.*" | head -1)
if [ -n "$backup_file" ]; then
    grep "^Disk /dev/" "$backup_file"
else
    echo "No backup file found"
fi

echo "Modified pt.parted file:"
grep "^Disk /dev/" "$TEST_IMAGE_DIR/sda-pt.parted"
echo

# Test 8: Test error handling
echo -e "${YELLOW}Test 8: Test error handling${NC}"
echo "Testing with non-existent image:"
./ocs-resize-image-size --ocsroot "$TEST_DIR" --info "non-existent-image" 2>&1 | head -1

echo "Testing with invalid size:"
./ocs-resize-image-size --ocsroot "$TEST_DIR" --dry-run "$TEST_IMAGE" "invalid" 2>&1 | head -1
echo

# Summary
echo -e "${GREEN}=== Test Summary ===${NC}"
echo "✓ Help display works"
echo "✓ Image info display works"
echo "✓ Dry run functionality works"
echo "✓ Image modification works"
echo "✓ Backup creation works"
echo "✓ Error handling works"
echo
echo -e "${BLUE}Test completed successfully!${NC}"
echo "Test files are in: $TEST_DIR"
echo "You can clean up with: rm -rf $TEST_DIR"
echo

# Show usage example
echo -e "${YELLOW}Example usage for real images:${NC}"
echo "# Show image info:"
echo "./ocs-resize-image-size --info my-real-image"
echo
echo "# Resize image (with backup):"
echo "./ocs-resize-image-size -v -b my-real-image 250"
echo
echo "# Restore with size check disabled:"
echo "ocs-sr -icds restoredisk my-real-image /dev/sdX"
