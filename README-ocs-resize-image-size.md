# ocs-resize-image-size

A script to modify the recorded size of a Clonezilla image so that it can be restored to a smaller drive.

## Overview

Clonezilla normally prevents restoring an image to a drive that is smaller than the original source drive. This script modifies the disk size information stored in the Clonezilla image metadata files, allowing the image to be restored to smaller drives.

## How It Works

Clonezilla stores disk size information in several files within each image:

1. **`{disk}-pt.parted`** - Contains partition table information including the original disk size in sectors
2. **`{disk}-pt.parted.compact`** - Compact version of the partition table
3. **`Info-img-size.txt`** - Contains general image size information

The script modifies these files to reflect a smaller disk size, effectively "tricking" Clone<PERSON> into thinking the image came from a smaller drive.

## Usage

### Basic Usage

```bash
# Modify an image to appear as a 120GB disk
./ocs-resize-image-size my-image 120

# Show current image information
./ocs-resize-image-size --info my-image

# Dry run to see what would be changed
./ocs-resize-image-size --dry-run my-image 120
```

### Advanced Usage

```bash
# Create backups and use verbose output
./ocs-resize-image-size -v -b my-image 120

# Force modification without confirmation
./ocs-resize-image-size -f my-image 120

# Use custom image directory
./ocs-resize-image-size --ocsroot /custom/path my-image 120
```

## Options

- `-h, --help` - Show help message
- `-v, --verbose` - Enable verbose output
- `-b, --backup` - Create backup of original files before modification
- `-f, --force` - Force modification without confirmation
- `-i, --info` - Show current image information and exit
- `--dry-run` - Show what would be changed without making changes
- `--ocsroot DIR` - Specify image root directory (default: /home/<USER>

## Important Notes

### Safety Considerations

1. **Always create backups** using the `-b` option before modifying images
2. **Test thoroughly** before using modified images in production
3. **Verify the target drive** has enough space for the actual data in the image
4. **Use dry-run first** to see what changes will be made

### Restoration Requirements

When restoring a modified image, you must use the `-icds` option to skip disk size checking:

```bash
ocs-sr -icds restoredisk my-image /dev/sdX
```

The `-icds` option tells Clonezilla to ignore the disk size check and proceed with restoration.

### Limitations

1. **Data size matters**: The script only changes the recorded disk size, not the actual data. Ensure the target drive has enough space for all partitions and data.

2. **Partition layout**: If the original image has partitions that extend close to the end of the disk, you may need to manually adjust partition sizes after restoration.

3. **File system compatibility**: Some file systems may have issues if restored to significantly smaller drives.

## Examples

### Example 1: Basic Image Resize

```bash
# Check current image information
./ocs-resize-image-size --info server-backup

# Modify image to appear as 500GB disk
./ocs-resize-image-size -v -b server-backup 500

# Restore to smaller drive
ocs-sr -icds restoredisk server-backup /dev/sdb
```

### Example 2: Safe Testing Workflow

```bash
# 1. Check current image
./ocs-resize-image-size --info my-image

# 2. Dry run to see changes
./ocs-resize-image-size --dry-run my-image 250

# 3. Create backup and modify
./ocs-resize-image-size -v -b my-image 250

# 4. Test restoration on non-critical system
ocs-sr -icds restoredisk my-image /dev/test-drive
```

## Technical Details

### File Modifications

The script modifies lines in `*-pt.parted` files that look like:
```
Disk /dev/sda: 976773168s
```

And changes them to reflect the new size:
```
Disk /dev/sda: 488386584s
```

### Size Calculations

- Input sizes are in GB (1000^3 bytes)
- Internal calculations use sectors (512 bytes each)
- Conversion: 1 GB ≈ 1,953,125 sectors

### Backup Files

When using the `-b` option, backup files are created with timestamps:
```
sda-pt.parted.backup.20241207_143022
```

## Troubleshooting

### Common Issues

1. **"No pt.parted files found"**
   - Verify the image directory exists and contains Clonezilla image files
   - Check that you have the correct image name

2. **"Failed to modify pt.parted correctly"**
   - Ensure you have write permissions to the image directory
   - Check that the pt.parted file format is as expected

3. **Restoration fails even with -icds**
   - Verify the target drive has enough space for the actual data
   - Check that all partitions fit within the new disk size

### Getting Help

Use the `--info` option to examine image structure:
```bash
./ocs-resize-image-size --info my-image
```

Use `--verbose` and `--dry-run` to understand what the script will do:
```bash
./ocs-resize-image-size --verbose --dry-run my-image 120
```

## License

This script is provided as-is for educational and practical use. Use at your own risk and always test thoroughly before production use.
