# Bug Fix: Remote Backup Function Output Handling

## Issue Description

The `copy_remote_backup_to_partimag` function was causing syntax errors in the main script due to improper output handling. The function was sending debug information and progress messages to stdout, which was being captured in variable assignments and causing parsing issues.

### Error Symptoms
```
./ocs-flyright: line 907: unexpected EOF while looking for matching `"'
./ocs-flyright: line 910: syntax error: unexpected end of file
```

### Root Cause
The function was outputting multiple lines to stdout:
```bash
new_backup_name=$(copy_remote_backup_to_partimag ...)
```

This captured output like:
```
Renaming remote backup from '2025-10-07-10-C208-RSI-Host-img' to '657-RSI Host-10_08_2025-1-img'
Preparing to copy remote backup from /mnt/remote_backup/2025-10-07-10-C208-RSI-Host-img to /home/<USER>/657-RSI Host-10_08_2025-1-img...
Removing existing backup directory: /home/<USER>/657-<PERSON>I Host-10_08_2025-1-img
Found 108 files to copy (total size: 330947043493 bytes)
657-RSI Host-10_08_2025-1-img
```

When this multi-line string was later used in the script, it caused syntax errors due to unmatched quotes and unexpected line breaks.

## Solution

### 1. Redirect Debug Output to stderr
All informational and debug messages in the `copy_remote_backup_to_partimag` function were redirected to stderr using `>&2`:

```bash
echo "Renaming remote backup from '$backup_dir' to '$new_backup_name'" >&2
echo "Preparing to copy remote backup from $source_path to $dest_path..." >&2
echo "Removing existing backup directory: $dest_path" >&2
```

### 2. Ensure Only Return Value Goes to stdout
The function now only outputs the backup name to stdout:

```bash
# Return the new backup name for use by the main script
echo "$new_backup_name"
```

### 3. Update Configuration Usage
Also updated the function to use configuration variables instead of hardcoded paths:

```bash
# Before
local database_path="/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
local table="Drives"

# After  
local database_path="$db_path"
local table="$table_name"
```

## Files Modified

1. **usr/sbin/ocs-flyright-functions**
   - Fixed `copy_remote_backup_to_partimag()` function output handling
   - Updated `create_log_database_for_remote()` to use configuration
   - Redirected all debug/progress output to stderr

## Testing

Created `test-remote-backup-function.sh` to verify proper stdout/stderr separation.

## Result

The remote backup functionality now works correctly without syntax errors. The variable assignment captures only the backup name, while all debug and progress information is properly displayed to the user via stderr.

### Before Fix
```bash
new_backup_name=$(copy_remote_backup_to_partimag ...)
# $new_backup_name contained multiple lines with debug info
```

### After Fix
```bash
new_backup_name=$(copy_remote_backup_to_partimag ...)
# $new_backup_name contains only: "657-RSI Host-10_08_2025-1-img"
```

This ensures that the variable can be safely used in subsequent script operations without causing syntax errors.

## Prevention

To prevent similar issues in the future:

1. **Always use stderr for debug output**: Use `>&2` for any informational messages
2. **Keep stdout clean**: Only output the intended return value to stdout
3. **Test function output**: Verify that functions return only what's expected
4. **Use configuration variables**: Avoid hardcoded paths in functions

This fix maintains the user experience (they still see all the progress information) while ensuring the script functions correctly.
