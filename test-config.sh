#!/bin/bash
#
# test-config.sh - Test script to verify INI configuration system
#
# This script tests the read_ini_value function and verifies that
# configuration values are being read correctly from the INI file.

# Configuration file path
CONFIG_FILE="/home/<USER>/Desktop/clonezilla-master/ocs-flyright.ini"

# Function to read INI file values (same as in the main scripts)
read_ini_value() {
    local section="$1"
    local key="$2"
    local config_file="${3:-$CONFIG_FILE}"
    
    if [ ! -f "$config_file" ]; then
        echo "Error: Configuration file $config_file not found" >&2
        return 1
    fi
    
    # Use awk to parse INI file
    awk -F' *= *' -v section="[$section]" -v key="$key" '
        $0 == section { in_section = 1; next }
        /^\[/ { in_section = 0; next }
        in_section && $1 == key { 
            # Remove any trailing comments
            gsub(/ *#.*$/, "", $2)
            # Remove any trailing whitespace
            gsub(/ *$/, "", $2)
            print $2
            exit
        }
    ' "$config_file"
}

echo "Testing INI Configuration System"
echo "================================"
echo

# Test database configuration
echo "Database Configuration:"
echo "  database_path: $(read_ini_value "database" "database_path")"
echo "  table_name: $(read_ini_value "database" "table_name")"
echo "  log_database_base_path: $(read_ini_value "database" "log_database_base_path")"
echo

# Test paths configuration
echo "Paths Configuration:"
echo "  drbl_script_path: $(read_ini_value "paths" "drbl_script_path")"
echo "  ocs_sr_path: $(read_ini_value "paths" "ocs_sr_path")"
echo "  functions_file: $(read_ini_value "paths" "functions_file")"
echo "  partimag_path: $(read_ini_value "paths" "partimag_path")"
echo "  remote_backup_mount: $(read_ini_value "paths" "remote_backup_mount")"
echo

# Test device serials
echo "Device Serial Numbers:"
echo "  c208: $(read_ini_value "device_serials" "c208")"
echo "  efis: $(read_ini_value "device_serials" "efis")"
echo "  ka_g1000: $(read_ini_value "device_serials" "ka_g1000")"
echo "  dash: $(read_ini_value "device_serials" "dash")"
echo "  remote_backup: $(read_ini_value "device_serials" "remote_backup")"
echo

# Test excluded WWIDs
echo "Excluded WWIDs:"
i=1
while true; do
    wwid=$(read_ini_value "excluded_wwids" "wwid_$i")
    if [ -z "$wwid" ]; then
        break
    fi
    echo "  wwid_$i: $wwid"
    ((i++))
done
echo

# Test simulator mappings
echo "Simulator Mappings:"
echo "  1018_archive: $(read_ini_value "simulator_mappings" "1018_archive")"
echo "  c208_archive: $(read_ini_value "simulator_mappings" "c208_archive")"
echo "  dash_archive: $(read_ini_value "simulator_mappings" "dash_archive")"
echo

# Test UI configuration
echo "UI Configuration:"
echo "  dialog_tool: $(read_ini_value "ui" "dialog_tool")"
echo "  language: $(read_ini_value "ui" "language")"
echo

# Test backup settings
echo "Backup Settings:"
echo "  backup_params: $(read_ini_value "backup_settings" "backup_params")"
echo "  restore_params: $(read_ini_value "backup_settings" "restore_params")"
echo "  dd_block_size: $(read_ini_value "backup_settings" "dd_block_size")"
echo

# Test SD card configuration
echo "SD Card Configuration:"
echo "  uhs2_id_instance: $(read_ini_value "sd_card" "uhs2_id_instance")"
echo "  uhs2_id_model: $(read_ini_value "sd_card" "uhs2_id_model")"
echo "  enable_uhs2_optimization: $(read_ini_value "sd_card" "enable_uhs2_optimization")"
echo

# Test dash system configuration
echo "Dash System Configuration:"
echo "  log_filename: $(read_ini_value "dash_system" "log_filename")"
echo "  cae0_suffix: $(read_ini_value "dash_system" "cae0_suffix")"
echo "  cae1_suffix: $(read_ini_value "dash_system" "cae1_suffix")"
echo

echo "Configuration test completed."
