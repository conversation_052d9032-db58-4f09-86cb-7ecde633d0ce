# Bug Fix: Log Database Table Creation Issue

## Issue Description

The `log_drive_action` function was failing with the error "no such table: logs" when trying to insert log entries. This occurred because:

1. Log database files were being created but without the required table structure
2. Computer names with spaces were not being properly handled in file paths
3. Permission issues prevented database creation/modification
4. No error handling for database creation failures

### Error Symptoms
```
Error: in prepare, no such table: logs (1)
```

### Root Causes

1. **Missing Table Structure**: Log database files existed but were empty (0 bytes) without the `logs` table
2. **Computer Name Handling**: Computer names like "RSI Host" were not being converted to "RSI_Host" for file paths consistently
3. **Permission Issues**: Database files created by root were not writable by the regular user
4. **No Error Recovery**: No graceful handling when database operations failed

## Solution

### 1. Enhanced Database Creation Logic

Updated `log_drive_action` function to:
- Check if log database exists and has the required table
- Create the table structure if missing
- Handle empty database files by recreating them
- Provide proper error handling and user feedback

```bash
# Ensure log database and table exist
local db_creation_needed=false
if [ ! -f "$log_path" ]; then
    echo "Creating log database: $log_path" >&2
    db_creation_needed=true
else
    # Check if logs table exists
    local table_exists=$(sqlite3 "$log_path" "SELECT name FROM sqlite_master WHERE type='table' AND name='logs';" 2>/dev/null)
    if [ -z "$table_exists" ]; then
        echo "Creating logs table in existing database: $log_path" >&2
        db_creation_needed=true
    fi
fi
```

### 2. Computer Name Normalization

Added consistent computer name cleaning in the logging function:

```bash
# Clean up computer name for file naming (replace spaces with underscores)
local clean_computer="${computer// /_}"
local log_filename="$clean_computer.db"
```

### 3. Permission Handling

Added permission management and error recovery:

```bash
# Remove existing empty database if it exists and recreate with proper permissions
if [ -f "$log_path" ] && [ ! -s "$log_path" ]; then
    rm -f "$log_path" 2>/dev/null || true
fi

# Ensure the database file has proper permissions
chmod 644 "$log_path" 2>/dev/null || true

if [ $? -ne 0 ]; then
    echo "Warning: Failed to create log database. Logging will be skipped." >&2
    whiptail --title "Warning" --msgbox "Failed to create log database. The operation completed successfully but logging was skipped." 8 60
    return 0
fi
```

### 4. Improved Error Handling

Enhanced error reporting for database operations:

```bash
local insert_result
insert_result=$(sqlite3 "$log_path" "INSERT INTO logs ..." 2>&1)

if [ $? -eq 0 ]; then
    whiptail --title "Success" --msgbox "Log entry successfully added." 8 50
else
    echo "Error inserting log entry: $insert_result" >&2
    whiptail --title "Warning" --msgbox "Failed to add log entry to database: $insert_result\n\nThe operation completed successfully but logging failed." 10 60
fi
```

### 5. Table Schema Definition

Standardized the logs table schema:

```sql
CREATE TABLE IF NOT EXISTS logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    "Letter/PriSec" TEXT,
    "Asset Tag" TEXT,
    "Serial Number" TEXT,
    "Log Comment" TEXT,
    "Action" TEXT,
    "Revision Number" INTEGER,
    "Date" TEXT,
    "Load Name" TEXT
);
```

## Files Modified

1. **usr/sbin/ocs-flyright-functions**
   - Enhanced `log_drive_action()` function with robust database creation
   - Added computer name normalization
   - Improved error handling and user feedback
   - Added permission management

## Testing

Created `test-log-database.sh` to verify:
- Log database creation
- Table structure validation
- Sample data insertion
- Permission handling

## Benefits

1. **Robust Database Creation**: Automatically creates missing databases and tables
2. **Error Recovery**: Gracefully handles permission and creation failures
3. **Consistent Naming**: Computer names are properly normalized for file paths
4. **User Feedback**: Clear error messages when logging fails
5. **Non-Blocking**: Main operations continue even if logging fails

## Prevention

To prevent similar issues in the future:

1. **Always check table existence** before database operations
2. **Handle permissions gracefully** with fallback options
3. **Normalize file names** consistently across all functions
4. **Provide user feedback** for database operations
5. **Test database operations** in isolation

## Result

The logging system now:
- Creates databases and tables automatically when needed
- Handles computer names with spaces correctly
- Provides clear error messages when issues occur
- Continues main operations even if logging fails
- Maintains data integrity and user experience

Remote backup operations will now complete successfully with proper logging, and users will be informed if any logging issues occur without blocking the main functionality.
