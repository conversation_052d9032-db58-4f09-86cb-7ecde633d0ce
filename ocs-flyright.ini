# ocs-flyright Configuration File
# This file contains all configurable variables for the Flyright Clonezilla system
# Variables are organized into logical sections for easy management

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
[database]
# Main drive database path
database_path = /home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db
# Database table name for drives
table_name = Drives
# Base path for simulator-specific log databases
log_database_base_path = /home/<USER>/Desktop/DriveDatabase/simulator_databases

# ============================================================================
# SYSTEM PATHS
# ============================================================================
[paths]
# DRBL script path
drbl_script_path = /usr/share/drbl
# Clonezilla ocs-sr command path
ocs_sr_path = /home/<USER>/Desktop/clonezilla-master/usr/sbin/ocs-sr
# Functions file path
functions_file = /home/<USER>/Desktop/clonezilla-master/usr/sbin/ocs-flyright-functions
# Partition image storage path
partimag_path = /home/<USER>
# Remote backup mount point
remote_backup_mount = /mnt/remote_backup
# Temporary files directory
temp_dir = /tmp

# ============================================================================
# CONFIGURATION FILES
# ============================================================================
[config_files]
# DRBL configuration files
drbl_conf_functions = /sbin/drbl-conf-functions
drbl_ocs_conf = /etc/drbl/drbl-ocs.conf
drbl_ocs_functions = /sbin/ocs-functions
ocs_live_conf = /etc/ocs/ocs-live.conf

# ============================================================================
# DEVICE SERIAL NUMBERS
# ============================================================================
[device_serials]
# Archive disk serial numbers for different simulators
c208 = Z4Z83RHP
efis = 9WM89ES0
ka_g1000 = Z1E1T00R
c90 = Z4Z9FNMZ
pl21 = WD-WCC4E3TA1SK8
dash = Z4E0B6E2
internal = WD-WCC6Y4EEKNNV
os = NJD518W1140090S39C
# Remote backup disk serial number
remote_backup = PAB471W1039670S33N

# ============================================================================
# WWID EXCLUSION LIST
# ============================================================================
[excluded_wwids]
# List of WWIDs to exclude from device selection
wwid_1 = naa.53a5a27678000f64
wwid_2 = naa.5000c5004ec221d9
wwid_3 = naa.5000c500a2e75db0
wwid_4 = naa.5000c5004ec9aec8
wwid_5 = naa.5000c5007a43f99e
wwid_6 = naa.5000c500a5523e8d
wwid_7 = naa.5000c500408fd9d9
wwid_8 = naa.50014ee20f256a95
wwid_9 = naa.50014ee20cad167b

# ============================================================================
# SIMULATOR MAPPINGS
# ============================================================================
[simulator_mappings]
# Archive disk selection to simulator name mappings
1018_archive = 1018
1329_archive = 1329
c208_archive = 657
dash_archive = 393_423
ka_g1000_archive = 1678_1697
c90_archive = 1477
internal_archive = VAPT

# ============================================================================
# USER INTERFACE
# ============================================================================
[ui]
# Dialog tool to use for user interface
dialog_tool = whiptail
# Language setting
language = en_US.UTF-8

# ============================================================================
# BACKUP/RESTORE SETTINGS
# ============================================================================
[backup_settings]
# Default ocs-sr parameters for backup operations
backup_params = -q2 -c -j2 -z1p -i 4096 -sfsck -scs -senc -p true
# Default ocs-sr parameters for restore operations
restore_params = -q2 -c -j2 -z1p -i 4096 -sfsck -scr -senc -p true
# DD backup block size
dd_block_size = 4M

# ============================================================================
# SD CARD CONFIGURATION
# ============================================================================
[sd_card]
# UHS-II slot identification parameters
uhs2_id_instance = 0:1
uhs2_id_model = SD_UHS_II
# Enable UHS-II optimization for SD card operations
enable_uhs2_optimization = true

# ============================================================================
# DASH SYSTEM CONFIGURATION
# ============================================================================
[dash_system]
# Special configuration for dash backup systems
log_filename = host.db
# CAE drive suffixes for dash systems
cae0_suffix = _CAE0
cae1_suffix = _CAE1

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
[logging]
# Enable debug output
debug_enabled = true
# Log file rotation settings
max_log_size = 10M
log_retention_days = 30

# ============================================================================
# SYSTEM DEVICE PATHS
# ============================================================================
[system_devices]
# System block device path pattern
block_device_pattern = /dev/sd*
# System block device info path pattern
sys_block_pattern = /sys/block/sd*
