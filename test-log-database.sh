#!/bin/bash
#
# test-log-database.sh - Test log database creation and table structure
#
# This script tests the log database creation functionality to ensure
# the logs table is properly created when needed.

# Source the configuration
CONFIG_FILE="/home/<USER>/Desktop/clonezilla-master/ocs-flyright.ini"

# Function to read INI file values
read_ini_value() {
    local section="$1"
    local key="$2"
    local config_file="${3:-$CONFIG_FILE}"
    
    if [ ! -f "$config_file" ]; then
        echo "Error: Configuration file $config_file not found" >&2
        return 1
    fi
    
    # Use awk to parse INI file
    awk -F' *= *' -v section="[$section]" -v key="$key" '
        $0 == section { in_section = 1; next }
        /^\[/ { in_section = 0; next }
        in_section && $1 == key { 
            # Remove any trailing comments
            gsub(/ *#.*$/, "", $2)
            # Remove any trailing whitespace
            gsub(/ *$/, "", $2)
            print $2
            exit
        }
    ' "$config_file"
}

echo "Testing Log Database Creation"
echo "============================="

# Load configuration
log_database_base_path=$(read_ini_value "database" "log_database_base_path")
log_database_base_path="${log_database_base_path:-/home/<USER>/Desktop/DriveDatabase/simulator_databases}"

echo "Log database base path: $log_database_base_path"

# Test parameters
test_simulator="657"
test_computer="RSI Host"
clean_computer="${test_computer// /_}"

echo "Test simulator: $test_simulator"
echo "Test computer: $test_computer"
echo "Clean computer name: $clean_computer"

# Test log database creation
log_dir="$log_database_base_path/$test_simulator"
log_filename="$clean_computer.db"
log_path="$log_dir/$log_filename"

echo "Expected log path: $log_path"

# Create test log database
echo
echo "Creating test log database..."

# Ensure log directory exists
if [ ! -d "$log_dir" ]; then
    echo "Creating log directory: $log_dir"
    mkdir -p "$log_dir"
fi

# Remove existing problematic database and recreate
if [ -f "$log_path" ]; then
    echo "Removing existing database: $log_path"
    rm -f "$log_path" 2>/dev/null || true
fi

# Create log database with proper table structure
echo "Creating log database: $log_path"
sqlite3 "$log_path" <<EOF
CREATE TABLE IF NOT EXISTS logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    "Letter/PriSec" TEXT,
    "Asset Tag" TEXT,
    "Serial Number" TEXT,
    "Log Comment" TEXT,
    "Action" TEXT,
    "Revision Number" INTEGER,
    "Date" TEXT,
    "Load Name" TEXT
);
EOF

if [ $? -eq 0 ]; then
    echo "✓ Log database created successfully"
    chmod 644 "$log_path" 2>/dev/null || true
else
    echo "✗ Failed to create log database"
    exit 1
fi

# Verify table structure
echo
echo "Verifying table structure..."
table_info=$(sqlite3 "$log_path" ".schema logs" 2>/dev/null)
if [ -n "$table_info" ]; then
    echo "✓ Logs table exists"
    echo "Table schema:"
    echo "$table_info"
else
    echo "✗ Logs table does not exist"
    exit 1
fi

# Test inserting a sample log entry
echo
echo "Testing log entry insertion..."
sample_date=$(date '+%m-%d-%y:%H:%M')
sqlite3 "$log_path" "INSERT INTO logs ('Letter/PriSec', 'Asset Tag', 'Serial Number', 'Log Comment', 'Action', 'Revision Number', 'Date', 'Load Name') VALUES ('Primary', 'AT-002005', 'S2RLNX0H806502H', 'Test log entry', 'Backup', 1, '$sample_date', 'test-backup-img');"

if [ $? -eq 0 ]; then
    echo "✓ Sample log entry inserted successfully"
    
    # Verify the entry was inserted
    entry_count=$(sqlite3 "$log_path" "SELECT COUNT(*) FROM logs;")
    echo "Total log entries: $entry_count"
    
    # Show the inserted entry
    echo "Sample entry:"
    sqlite3 "$log_path" "SELECT * FROM logs ORDER BY id DESC LIMIT 1;"
else
    echo "✗ Failed to insert sample log entry"
    exit 1
fi

echo
echo "Log database test completed successfully!"
echo "The log_drive_action function should now work correctly."
