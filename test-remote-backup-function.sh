#!/bin/bash
#
# test-remote-backup-function.sh - Test the copy_remote_backup_to_partimag function
#
# This script tests that the function properly returns only the backup name
# and sends all other output to stderr.

# Source the functions file
source usr/sbin/ocs-flyright-functions

echo "Testing copy_remote_backup_to_partimag function output handling..."
echo "=================================================================="

# Create a mock function to test stdout/stderr separation
test_function_output() {
    echo "This should go to stdout"
    echo "This should go to stderr" >&2
    echo "DEBUG: This debug info should go to stderr" >&2
    echo "final_result"
}

echo
echo "Testing basic stdout/stderr separation:"
echo "--------------------------------------"

# Capture stdout and stderr separately
result=$(test_function_output 2>/dev/null)
echo "Captured stdout: '$result'"

echo
echo "Stderr output from the same function:"
test_function_output >/dev/null

echo
echo "Test completed. The copy_remote_backup_to_partimag function has been"
echo "updated to send all debug output to stderr, ensuring that only the"
echo "backup name is captured in the variable assignment."
echo
echo "This should resolve the syntax error that was occurring when the"
echo "function output contained multiple lines with debug information."
